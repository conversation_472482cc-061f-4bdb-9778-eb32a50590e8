#!/bin/bash

echo "🔧 Quick Build Test for Drivers Android App"
echo "============================================"

# Check if we're in the right directory
if [ ! -f "build.gradle.kts" ]; then
    echo "❌ Error: Not in project root directory"
    exit 1
fi

echo "🧹 Cleaning project..."
./gradlew clean

echo ""
echo "🏗️  Testing resource compilation..."
./gradlew processDebugResources

if [ $? -eq 0 ]; then
    echo "✅ Resources compiled successfully!"
    echo ""
    echo "🏗️  Testing full build..."
    ./gradlew assembleDebug
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "🎉 BUILD SUCCESSFUL!"
        echo "==================="
        echo "✅ All theme issues resolved"
        echo "✅ Material Components working"
        echo "✅ Resources compiled correctly"
        echo "✅ APK generated successfully"
        echo ""
        echo "📱 Ready to run in Android Studio!"
    else
        echo ""
        echo "❌ Full build failed - check errors above"
    fi
else
    echo "❌ Resource compilation failed - theme issues remain"
    echo ""
    echo "🔍 Common solutions:"
    echo "1. Ensure Material Components dependency is included"
    echo "2. Check theme parent names are correct"
    echo "3. Verify all color references exist"
fi
