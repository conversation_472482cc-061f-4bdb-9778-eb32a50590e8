# Project Plan: iOS (Swift) to Android (<PERSON><PERSON><PERSON>) App Conversion

## 1. Overview

This document outlines the steps and considerations for an AI agent to convert an existing iOS application (written in Swift) into a native Android application using Kotlin and modern Android development practices, primarily Jetpack Compose for the UI.

**iOS App Structure (Based on provided files):**
*   **Models:** `LoginModels.swift`, `RouteModels.swift`
    *   Key structs: `LoginRequest`, `LoginResponse`, `Route`, `Stop`, `DriverAppRouteListItemModel`, `HaulierRouteDetailModel`, `RouteLineModel`, `RouteLinePhotoModel`, `StopCompletionRequest`, `DriverAppViewRouteModel`, `DriverAppStopModel`. Includes data conversion extensions.
*   **ViewModels:** `RoutesViewModel.swift`
    *   Manages authentication, loading states, API token, list of user routes (`userRoutes`), and selected route details (`selectedRouteDetail`).
    *   Handles API calls to `/Auth/LoginApp`, `/DriverApp/ListRoutesApp`, `/HaulierRoute/GetRoute`.
*   **Views:** `LoginView.swift`, `RoutesListView.swift`, `RouteDetailView.swift`, `StopDetailView.swift`
    *   `LoginView`: UI for email/password, uses `RoutesViewModel.shared`.
    *   `RoutesListView`: Lists routes, navigates to `RouteDetailView`.
    *   `RouteDetailView`: Uses its own `RouteDetailViewModel`, displays route summary and stops, navigates to `StopDetailWrapper` then `StopDetailView`.
    *   `StopDetailView`: Highly complex UI for stop details, including map, tonnage input, image/signature capture, and API calls for stop completion, photo/signature upload.

**Target Android App:**
*   **Language:** Kotlin
*   **UI Toolkit:** Jetpack Compose
*   **Architecture:** MVVM (Model-View-ViewModel)
*   **Key Libraries:** Kotlin Coroutines & Flow, Android Jetpack (ViewModel, Navigation, Lifecycle), Retrofit (for networking), DataStore (for preferences/token storage), Coil/Glide (for image loading), Accompanist libraries (for permissions, system UI, etc. as needed).
*   **API Base URL (inferred):** `https://app-weigh-technology-api.azurewebsites.net/`

## 2. Android Project Setup

1.  **Create New Project:**
    *   Use Android Studio.
    *   Select "Empty Compose Activity" template.
    *   Language: Kotlin.
    *   Minimum SDK: API 24 (Nougat) or higher (recommended for broad compatibility with Jetpack Compose features and modern APIs).

2.  **Configure `build.gradle` (Module: app):**
    *   Ensure Kotlin version is up-to-date.
    *   Add necessary Jetpack Compose dependencies (use the latest stable versions from the Compose BOM).
    *   Add ViewModel, LiveData/StateFlow, Navigation Compose, Coroutines, Retrofit, Gson/Moshi/Kotlinx Serialization, OkHttp (with logging interceptor), Jetpack DataStore, and image loading library (e.g., Coil).
    *   Enable Jetpack Compose in the `android` block (`buildFeatures { compose = true }` and `composeOptions { kotlinCompilerExtensionVersion = "..." }`).
    *   If using Hilt for dependency injection, add Hilt dependencies and configure the KSP plugin.

3.  **Project Structure (Proposed):**
    ```
    app/
    └── src/
        └── main/
            ├── java/com/yourcompany/driversapp/  (Base package)
            │   ├── MainActivity.kt                 (Entry point, hosts Compose content)
            │   ├── DriversApplication.kt           (Optional: For DI setup, app-wide init)
            │   ├── data/
            │   │   ├── model/                      (Kotlin data classes, e.g., LoginRequest.kt, LoginResponse.kt, Route.kt, Stop.kt, etc.)
            │   │   ├── remote/                     (Network layer)
            │   │   │   ├── ApiService.kt           (Retrofit interface for all endpoints)
            │   │   │   ├── dto/                    (Data Transfer Objects if API models differ significantly from domain models)
            │   │   │   └── NetworkModule.kt        (Hilt module for Retrofit, OkHttpClient)
            │   │   ├── local/                      (Local data sources)
            │   │   │   └── UserPreferencesManager.kt (Using Jetpack DataStore for API token, user info)
            │   │   └── repository/                 (Consolidates data sources)
            │   │       ├── AuthRepository.kt
            │   │       └── RoutesRepository.kt
            │   ├── ui/
            │   │   ├── theme/                      (Compose theme: Color.kt, Shape.kt, Theme.kt, Typography.kt)
            │   │   ├── navigation/                 (Navigation graph and routes: AppNavigation.kt, Screen.kt)
            │   │   ├── viewmodel/                  (Android ViewModels: AuthViewModel.kt, RoutesListViewModel.kt, RouteDetailViewModel.kt, StopDetailViewModel.kt)
            │   │   └── screen/                     (Composable screens)
            │   │       ├── login/LoginScreen.kt
            │   │       ├── routeslist/RoutesListScreen.kt
            │   │       ├── routedetail/RouteDetailScreen.kt
            │   │       └── stopdetail/
            │   │           ├── StopDetailScreen.kt
            │   │           ├── composables/          (Smaller UI parts like MapViewComposable.kt, SignaturePadComposable.kt, ImagePickerComposable.kt)
            │   │   ├── components/                 (Reusable UI components across screens)
            │   └── di/                             (Dependency Injection modules if using Hilt)
            │       ├── AppModule.kt
            │       └── ViewModelModule.kt
            └── res/                                (Android resources)
                ├── drawable/                       (Icons, images - use VectorDrawables where possible)
                ├── values/
                │   ├── strings.xml
                │   ├── colors.xml                  (Primarily for pre-Compose system UI, Compose uses Theme.kt)
                │   └── themes.xml                  (App theme, ensure parent is Theme.MaterialComponents.DayNight.NoActionBar or similar)
    ```

## 3. Porting Models (from `Drivers/Models/*.swift`)

*   **Objective:** Convert Swift structs to Kotlin `data class`. Pay close attention to nullability (`?` in Swift maps to `?` in Kotlin) and data types (e.g., `UUID` to `String` or `java.util.UUID`, `Date` to `String` for API transfer or `java.time.ZonedDateTime` / `kotlinx.datetime.Instant` for domain logic).
*   **Files to Create (in `data/model/`):**
    *   `LoginModels.kt`: `LoginRequest`, `LoginResponse`.
    *   `RouteModels.kt`: `Route`, `Stop`, `DriverAppRouteListItem`, `HaulierRouteDetail`, `RouteLine`, `RouteLinePhoto`, `StopCompletionRequest`, `DriverAppViewRoute`, `DriverAppStop`.
        *   For `Codable` structs, use `@Serializable` with Kotlinx Serialization or `@SerializedName` with Gson/Moshi for Retrofit.
        *   Replicate any computed properties or helper functions (like the `toStop()` conversions) as extension functions or methods in Kotlin.
        *   Example `Stop.kt`:
            ```kotlin
            package com.yourcompany.driversapp.data.model

            import kotlinx.serialization.Serializable // if using Kotlinx Serialization

            @Serializable
            data class Stop(
                val id: String, // Assuming UUID is stored as String
                val address: String,
                val customerName: String,
                val productDescription: String,
                val scheduledTime: String, // Or a proper date/time type
                val tonnesAmount: Double,
                var grossTonnage: Double? = null,
                var tareTonnage: Double? = null,
                var isCompleted: Boolean,
                var photoUrl: String? = null, // URL as String
                var signatureImageBase64: String? = null, // Store image data as Base64 String for API
                var signatureName: String? = null
            ) {
                val netTonnage: Double?
                    get() = if (grossTonnage != null && tareTonnage != null) grossTonnage!! - tareTonnage!! else null
            }
            ```

## 4. Porting ViewModels (from `Drivers/ViewModels/*.swift`)

*   **Objective:** Convert Swift `ObservableObject` classes to Android `ViewModel`s. Use Kotlin Coroutines for async operations and `StateFlow` / `SharedFlow` for exposing UI state.
*   **Files to Create (in `ui/viewmodel/`):**
    *   `AuthViewModel.kt`: Manages login logic, API token.
        *   Exposes `loginUiState: StateFlow<LoginUiState>` (e.g., sealed interface for Idle, Loading, Success, Error).
        *   `login(email, password)` function calls `AuthRepository`.
        *   Saves/clears token using `UserPreferencesManager`.
    *   `RoutesListViewModel.kt`: (Corresponds to parts of `RoutesViewModel.swift`)
        *   Fetches and displays the list of routes (`DriverAppRouteListItem`).
        *   Exposes `routesListUiState: StateFlow<RoutesListUiState>`.
        *   `fetchUserRoutes()` calls `RoutesRepository`.
        *   Handles logout.
    *   `RouteDetailViewModel.kt`: (Corresponds to `RouteDetailViewModel` in Swift)
        *   Fetches details for a specific route (`HaulierRouteDetail` converted to `DriverAppViewRoute`).
        *   Exposes `routeDetailUiState: StateFlow<RouteDetailUiState>`.
        *   `fetchRouteDetail(routeId)` calls `RoutesRepository`.
    *   `StopDetailViewModel.kt`: (Manages logic for `StopDetailView.swift`)
        *   Manages state for the `Stop` being detailed (gross/tare tonnage, photo, signature).
        *   Exposes `stopDetailUiState: StateFlow<StopDetailUiState>`.
        *   Methods for:
            *   `updateGrossTonnage(value: Double)`
            *   `updateTareTonnage(value: Double)`
            *   `setPhoto(imageData: ByteArray)`
            *   `setSignature(imageData: ByteArray, name: String)`
            *   `markAsComplete()`: Constructs `StopCompletionRequest` and calls `RoutesRepository` to interact with `/HaulierStop/CompleteStopV3` or `/HaulierStop/CompleteTipStopV3`.
            *   `uploadPhoto(imageData: ByteArray)`: Calls `RoutesRepository` for `/HaulierStop/UploadPhotoV2`.
            *   `uploadSignature(imageData: ByteArray, name: String)`: Calls `RoutesRepository` for `/HaulierStop/UploadSignatureV2`.

## 5. Porting Views (from `Drivers/Views/*.swift`) to Jetpack Compose Screens

*   **Objective:** Recreate UI and interactions using Jetpack Compose.
*   **Files to Create (in `ui/screen/` sub-packages):**
    *   `login/LoginScreen.kt`:
        *   `TextField`s for email/password, `Button` for login.
        *   Observes `AuthViewModel.loginUiState` for loading/error states.
        *   Navigates to routes list on success.
    *   `routeslist/RoutesListScreen.kt`:
        *   `Scaffold` with `TopAppBar`. `LazyColumn` to display `DriverAppRouteListItem`s.
        *   Observes `RoutesListViewModel.routesListUiState`.
        *   Each item navigates to `RouteDetailScreen`.
        *   Logout button in `TopAppBar`.
    *   `routedetail/RouteDetailScreen.kt`:
        *   Displays route summary and a `LazyColumn` of stops from `RouteDetailViewModel.routeDetailUiState`.
        *   Each stop item navigates to `StopDetailScreen`.
    *   `stopdetail/StopDetailScreen.kt`: This is the most complex.
        *   `Scaffold` with `TopAppBar` showing stop number/customer.
        *   Layout using `Column`, `Row`, `Card`, etc. to display:
            *   Map: Implement `MapViewComposable` using `AndroidView` to embed Google Maps SDK or use a Compose-specific map library if available and suitable. Geocode address to show marker.
            *   Stop Info: Customer name, address, product description, scheduled time.
            *   Tonnage Inputs: `TextField`s for Gross and Tare, styled for numeric input, with "kg" labels. Input validation (e.g., max value, numeric).
            *   Net Tonnage: Display calculated net tonnage.
            *   Photo Section: Button to "Add Photo". Displays selected/taken photo. Logic for `ImagePickerComposable` (using `rememberLauncherForActivityResult` for camera/gallery).
            *   Signature Section: Button to "Add Signature". Displays signature. Logic for `SignaturePadComposable` (custom canvas drawing).
            *   "Mark as Complete" Button: Calls `StopDetailViewModel.markAsComplete()`. Handles loading/error states.
        *   Observes `StopDetailViewModel.stopDetailUiState`.
        *   Handles runtime permissions for Camera and Location.

## 6. Implementing Core Functionality

*   **Networking (`data/remote/ApiService.kt`, `data/repository/*Repository.kt`):**
    *   `ApiService.kt`: Retrofit interface with suspend functions for all endpoints:
        *   `POST /Auth/LoginApp` (body: `LoginRequest`, returns `LoginResponse`)
        *   `POST /DriverApp/ListRoutesApp` (returns `List<DriverAppRouteListItem>`)
        *   `POST /HaulierRoute/GetRoute?routeId={id}` (returns `HaulierRouteDetail`)
        *   `POST /HaulierStop/CompleteStopV3?haulierStopId={id}` (body: `StopCompletionRequest`)
        *   `POST /HaulierStop/CompleteTipStopV3?tipStopId={id}` (body: `StopCompletionRequest`)
        *   `POST /HaulierStop/UploadPhotoV2?haulierStopId={id}` (multipart, for image data)
        *   `POST /HaulierStop/UploadSignatureV2?haulierStopId={id}` (multipart, for signature image and name)
    *   Authentication: Implement an OkHttp `Interceptor` to add the "Authorization: Bearer {token}" header, fetching the token from `UserPreferencesManager`.
    *   `AuthRepository.kt`: Calls login endpoint, saves/clears token.
    *   `RoutesRepository.kt`: Calls routes, route detail, stop completion, and upload endpoints. Handles data mapping if DTOs are used.

*   **Data Persistence (`data/local/UserPreferencesManager.kt`):**
    *   Use Jetpack DataStore (Preferences DataStore) to store/retrieve API token and potentially basic user info.
    *   Expose token as a `Flow<String?>`.

*   **Navigation (`ui/navigation/AppNavigation.kt`, `ui/navigation/Screen.kt`):**
    *   Use Jetpack Navigation Compose.
    *   `Screen.kt`: Sealed class defining all navigation routes (e.g., `Login`, `RoutesList`, `RouteDetail/{routeId}`, `StopDetail/{stopId}`).
    *   `AppNavigation.kt`: Composable function with `NavHost`.
    *   `MainActivity.kt`: Sets up `AppNavHost`. Determines start destination based on token availability (Login or RoutesList).

## 7. UI/UX Adaptation and Resources

*   **Material Design 3:** Adapt iOS UI to Material Design 3 principles. Use `MaterialTheme` and M3 components.
*   **Theme (`ui/theme/`):** Define `Color.kt` (with `ColorSchemes`), `Typography.kt`, `Shape.kt`.
*   **Strings:** Externalize all UI strings to `res/values/strings.xml`.
*   **Drawables:** Convert/add icons (preferably VectorDrawables) and images.
*   **Permissions:** Implement runtime permission requests for `ACCESS_FINE_LOCATION` (for map) and `CAMERA` (for photo capture) using `rememberLauncherForActivityResult` and providing rationale.

## 8. Assumptions & Information Required by AI Agent (for optimal conversion)

1.  **Full Source Code:** Access to the complete iOS project is ideal.
2.  **Precise API Specifications (if different from inferred):**
    *   Exact request/response models for all endpoints.
    *   Specifics of multipart uploads (field names for image/signature data).
    *   Detailed error response formats.
3.  **Assets:** Original image assets, icons, font files (if custom).
4.  **Third-Party iOS Libraries:** List any used for specific functionalities to find Android equivalents (e.g., if a specific mapping library was used beyond MapKit).
5.  **Detailed Business Logic:** Clarification on any complex calculations or rules not immediately obvious from the code (e.g., specific conditions for "Tip Stop" vs. regular stop completion).

## 9. Step-by-Step Implementation Guide for AI

1.  **Setup:** Create Android project, configure `build.gradle`, set up project structure.
2.  **Theme & Basic Shell:** Implement `Theme.kt`, `MainActivity` with basic `NavHost`.
3.  **Models:** Create all Kotlin data classes in `data/model/`.
4.  **Networking Core:**
    *   Implement `ApiService.kt` with all endpoints.
    *   Set up Retrofit, OkHttpClient (with Auth Interceptor).
    *   Implement `UserPreferencesManager` for token storage.
5.  **Repositories:** Implement `AuthRepository.kt` and `RoutesRepository.kt`.
6.  **ViewModels:** Implement `AuthViewModel`, `RoutesListViewModel`, `RouteDetailViewModel`, `StopDetailViewModel`.
7.  **Screens (Iterative, with ViewModel connection):**
    *   `LoginScreen.kt`
    *   `RoutesListScreen.kt`
    *   `RouteDetailScreen.kt`
    *   `StopDetailScreen.kt` (Break this into smaller composables: map, info section, tonnage inputs, photo handler, signature pad).
8.  **Navigation:** Fully implement `AppNavigation.kt` and integrate.
9.  **Core Functionality Implementation:**
    *   Wire up API calls in ViewModels/Repositories for all features (login, fetching data, stop completion, uploads).
    *   Implement image picking/capture and signature drawing.
    *   Implement map display and geocoding.
    *   Implement permission handling.
10. **Testing:** Write unit tests for ViewModels and Repositories. Write UI tests for key Compose screens and user flows.
11. **Polishing:** Refine UI/UX, handle all loading/error/empty states, ensure responsiveness.

This plan provides a comprehensive roadmap. The AI agent should proceed step-by-step, focusing on one layer or feature at a time.