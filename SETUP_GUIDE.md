# Setup Guide for Drivers Android App

## 🔧 Prerequisites Setup

### 1. Install Java Development Kit (JDK)

The project requires JDK 11 or later. Here are the installation options:

#### Option A: Install via Homebrew (Recommended for Mac)
```bash
# Install Homebrew if you don't have it
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install OpenJDK 17 (recommended)
brew install openjdk@17

# Add to your shell profile (~/.zshrc or ~/.bash_profile)
echo 'export PATH="/opt/homebrew/opt/openjdk@17/bin:$PATH"' >> ~/.zshrc
echo 'export JAVA_HOME="/opt/homebrew/opt/openjdk@17"' >> ~/.zshrc

# Reload your shell
source ~/.zshrc
```

#### Option B: Install via Oracle or Adoptium
1. Download JDK 17 from [Adoptium](https://adoptium.net/) or [Oracle](https://www.oracle.com/java/technologies/downloads/)
2. Install the downloaded package
3. Set JAVA_HOME environment variable

### 2. Verify Java Installation
```bash
java -version
javac -version
echo $JAVA_HOME
```

You should see output similar to:
```
openjdk version "17.0.x" 2023-xx-xx
OpenJDK Runtime Environment (build 17.0.x+x)
OpenJDK 64-Bit Server VM (build 17.0.x+x, mixed mode, sharing)
```

### 3. Install Android Studio
1. Download from [Android Studio](https://developer.android.com/studio)
2. Install and run the setup wizard
3. Install Android SDK (API level 24 or higher)

## 🚀 Project Setup

### 1. Open the Project
1. Open Android Studio
2. Choose "Open an existing project"
3. Navigate to `/Users/<USER>/Code/Drivers-Android`
4. Click "Open"

### 2. Sync Project
1. Android Studio should automatically prompt to sync
2. If not, click the "Sync Project with Gradle Files" button (🐘 icon)
3. Wait for sync to complete

### 3. Configure API Base URL
Update the API base URL in:
`app/src/main/java/com/yourcompany/driversapp/data/remote/NetworkModule.kt`

```kotlin
val baseUrl = "https://your-actual-api-url.com/api/"
```

### 4. Verify Build (Optional)
Run the verification script to test the build:
```bash
./verify-build.sh
```

### 5. Build and Run
1. Select a device or emulator
2. Click the "Run" button (▶️) or press Ctrl+R (Cmd+R on Mac)

## 🔍 Troubleshooting

### KSP Plugin Issues
If you encounter KSP plugin errors:
1. Ensure you have the correct Java version installed
2. Check that `gradle/libs.versions.toml` has compatible versions
3. Clean and rebuild: `./gradlew clean build`

**Fixed Issues:**
- ✅ Removed incompatible `kotlin-compose` plugin
- ✅ Updated KSP version to `1.9.25-1.0.20` (matches Kotlin 1.9.25)
- ✅ All plugin versions are now compatible

### Gradle Issues
```bash
# Clean the project
./gradlew clean

# Refresh dependencies
./gradlew --refresh-dependencies

# Build the project
./gradlew build
```

### Android Studio Issues
1. File → Invalidate Caches and Restart
2. Delete `.gradle` folder in project root
3. Sync project again

## 📱 Running the App

### Prerequisites for Full Functionality
1. **Camera Permission**: For photo capture
2. **Location Permission**: For map features
3. **Internet Connection**: For API calls

### Test Data
The app includes mock data for testing when API is not available.

## 🧪 Running Tests

```bash
# Unit tests
./gradlew test

# Android instrumented tests
./gradlew connectedAndroidTest
```

## 📋 Next Steps

1. **Configure API**: Update base URL and test endpoints
2. **Test Features**: Try login, routes, and stop completion
3. **Customize UI**: Modify colors, strings, and branding
4. **Add Features**: Extend functionality as needed

## 🆘 Getting Help

If you encounter issues:
1. Check this setup guide
2. Review error messages in Android Studio
3. Check the `PROJECT_PROGRESS.md` for implementation details
4. Ensure all prerequisites are properly installed

## 🎯 Quick Start Checklist

- [ ] Java 11+ installed and configured
- [ ] Android Studio installed
- [ ] Project opened in Android Studio
- [ ] Gradle sync completed successfully
- [ ] API base URL configured
- [ ] App builds without errors
- [ ] App runs on device/emulator

Once all items are checked, your Drivers Android App should be ready for development and testing!
