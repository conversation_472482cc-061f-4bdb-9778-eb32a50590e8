# Drivers Android App

A modern Android application for drivers to manage routes, stops, and deliveries. This app is built using the latest Android development technologies and follows best practices for architecture and user experience.

## 🚀 Features

### Core Functionality
- **User Authentication** - Secure login with token-based authentication
- **Route Management** - View assigned routes and their details
- **Stop Management** - Complete stops with tonnage tracking
- **Photo Capture** - Take photos of deliveries using camera or gallery
- **Digital Signatures** - Capture customer signatures with custom drawing pad
- **Location Services** - View stop locations on map
- **Offline Support** - Local data storage with DataStore

### Technical Features
- **Modern UI** - Built with Jetpack Compose and Material Design 3
- **Reactive Architecture** - MVVM pattern with StateFlow and Coroutines
- **Dependency Injection** - Hilt for clean dependency management
- **Network Layer** - Retrofit with OkHttp for API communication
- **Permission Handling** - Runtime permission requests with user-friendly dialogs
- **Error Handling** - Comprehensive error states and user feedback
- **Testing** - Unit tests for ViewModels and Repositories

## 🏗️ Architecture

The app follows Clean Architecture principles with clear separation of concerns:

```
app/
├── data/
│   ├── model/          # Data classes and DTOs
│   ├── remote/         # API service and network configuration
│   ├── local/          # Local storage (DataStore)
│   └── repository/     # Data repositories
├── ui/
│   ├── screen/         # Compose screens
│   ├── viewmodel/      # ViewModels with UI state management
│   ├── navigation/     # Navigation configuration
│   ├── theme/          # Material Design 3 theming
│   └── components/     # Reusable UI components
└── di/                 # Dependency injection modules
```

## 🛠️ Tech Stack

- **Language**: Kotlin
- **UI Framework**: Jetpack Compose
- **Architecture**: MVVM with Clean Architecture
- **Dependency Injection**: Hilt
- **Networking**: Retrofit + OkHttp
- **Serialization**: Kotlinx Serialization
- **Local Storage**: Jetpack DataStore
- **Async Programming**: Coroutines + Flow
- **Navigation**: Navigation Compose
- **Testing**: JUnit, MockK, Coroutines Test

## 📱 Screens

1. **Login Screen** - User authentication
2. **Routes List** - Display all assigned routes
3. **Route Detail** - Show route information and stops
4. **Stop Detail** - Complete stops with:
   - Tonnage input (gross/tare/net)
   - Photo capture
   - Digital signature
   - Location display

## 🔧 Setup

### Prerequisites
- Android Studio Hedgehog or later
- JDK 11 or later
- Android SDK 24+ (API level 24)

### Configuration
1. Clone the repository
2. Open in Android Studio
3. Update the API base URL in `NetworkModule.kt`
4. Sync project with Gradle files
5. Run the app

### API Configuration
Update the base URL in `app/src/main/java/com/yourcompany/driversapp/data/remote/NetworkModule.kt`:

```kotlin
val baseUrl = "https://your-actual-api-url.com/api/"
```

## 🧪 Testing

Run unit tests:
```bash
./gradlew test
```

Run instrumented tests:
```bash
./gradlew connectedAndroidTest
```

## 📋 Permissions

The app requires the following permissions:
- **INTERNET** - API communication
- **CAMERA** - Photo capture
- **ACCESS_FINE_LOCATION** - Location services
- **READ_EXTERNAL_STORAGE** - Gallery access

## 🎨 Design

The app follows Material Design 3 guidelines with:
- Dynamic color theming
- Consistent spacing and typography
- Accessible touch targets
- Proper loading and error states

## 🔄 State Management

Each screen has its own ViewModel that manages:
- UI state with sealed classes/interfaces
- Loading states
- Error handling
- Data transformations

## 📦 Build Variants

- **Debug** - Development build with logging
- **Release** - Production build with optimizations

## 🤝 Contributing

1. Follow the existing code style
2. Write unit tests for new features
3. Update documentation as needed
4. Test on multiple device sizes

## 📄 License

This project is proprietary software. All rights reserved.

## 🔗 Related Documentation

- [Project Progress](PROJECT_PROGRESS.md) - Detailed development progress
- [Android Instructions](android-app-instructions.md) - Original conversion instructions
