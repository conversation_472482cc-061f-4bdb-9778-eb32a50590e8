package com.yourcompany.driversapp.data.repository

import com.yourcompany.driversapp.data.model.DriverAppRouteListItem
import com.yourcompany.driversapp.data.model.HaulierRouteDetail
import com.yourcompany.driversapp.data.model.StopCompletionRequest
import com.yourcompany.driversapp.data.remote.ApiService
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import okhttp3.ResponseBody
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import retrofit2.Response

class RoutesRepositoryTest {

    private lateinit var apiService: ApiService
    private lateinit var repository: RoutesRepository

    @Before
    fun setup() {
        apiService = mockk()
        repository = RoutesRepository(apiService)
    }

    @Test
    fun `getRoutes success returns routes list`() = runTest {
        // Given
        val mockRoutes = listOf(
            DriverAppRouteListItem(
                id = "1",
                routeName = "Route 1",
                customerName = "Customer 1",
                scheduledDate = "2024-01-01",
                status = "Active",
                totalStops = 5,
                completedStops = 2
            )
        )
        val mockResponse = Response.success(mockRoutes)
        coEvery { apiService.getRoutes() } returns mockResponse

        // When
        val result = repository.getRoutes()

        // Then
        assertTrue(result.isSuccess)
        assertEquals(mockRoutes, result.getOrNull())
        coVerify { apiService.getRoutes() }
    }

    @Test
    fun `getRoutes failure returns error`() = runTest {
        // Given
        val mockResponse = Response.error<List<DriverAppRouteListItem>>(
            404,
            ResponseBody.create(null, "Not found")
        )
        coEvery { apiService.getRoutes() } returns mockResponse

        // When
        val result = repository.getRoutes()

        // Then
        assertTrue(result.isFailure)
        assertTrue(result.exceptionOrNull()?.message?.contains("Failed to fetch routes") == true)
    }

    @Test
    fun `getRouteDetail success returns converted route`() = runTest {
        // Given
        val routeId = "route123"
        val mockRouteDetail = HaulierRouteDetail(
            id = routeId,
            routeName = "Test Route",
            customerName = "Test Customer",
            scheduledDate = "2024-01-01",
            status = "Active",
            routeLines = emptyList()
        )
        val mockResponse = Response.success(mockRouteDetail)
        coEvery { apiService.getRouteDetail(routeId) } returns mockResponse

        // When
        val result = repository.getRouteDetail(routeId)

        // Then
        assertTrue(result.isSuccess)
        val driverAppRoute = result.getOrNull()
        assertEquals(routeId, driverAppRoute?.id)
        assertEquals("Test Route", driverAppRoute?.routeName)
        coVerify { apiService.getRouteDetail(routeId) }
    }

    @Test
    fun `completeStop success returns unit`() = runTest {
        // Given
        val stopId = "stop123"
        val request = StopCompletionRequest(
            stopId = stopId,
            grossTonnage = 25.0,
            tareTonnage = 5.0,
            isCompleted = true
        )
        val mockResponse = Response.success(ResponseBody.create(null, ""))
        coEvery { apiService.completeStop(stopId, request) } returns mockResponse

        // When
        val result = repository.completeStop(stopId, request)

        // Then
        assertTrue(result.isSuccess)
        coVerify { apiService.completeStop(stopId, request) }
    }

    @Test
    fun `uploadPhoto success returns unit`() = runTest {
        // Given
        val stopId = "stop123"
        val imageData = byteArrayOf(1, 2, 3, 4, 5)
        val mockResponse = Response.success(ResponseBody.create(null, ""))
        coEvery { apiService.uploadPhoto(any(), any()) } returns mockResponse

        // When
        val result = repository.uploadPhoto(stopId, imageData)

        // Then
        assertTrue(result.isSuccess)
        coVerify { apiService.uploadPhoto(any(), any()) }
    }

    @Test
    fun `uploadSignature success returns unit`() = runTest {
        // Given
        val stopId = "stop123"
        val signatureData = byteArrayOf(1, 2, 3, 4, 5)
        val signatureName = "John Doe"
        val mockResponse = Response.success(ResponseBody.create(null, ""))
        coEvery { apiService.uploadSignature(any(), any(), any()) } returns mockResponse

        // When
        val result = repository.uploadSignature(stopId, signatureData, signatureName)

        // Then
        assertTrue(result.isSuccess)
        coVerify { apiService.uploadSignature(any(), any(), any()) }
    }
}
