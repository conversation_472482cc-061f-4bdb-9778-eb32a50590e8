package com.yourcompany.driversapp.ui.viewmodel

import com.yourcompany.driversapp.data.repository.AuthRepository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

@OptIn(ExperimentalCoroutinesApi::class)
class AuthViewModelTest {

    private lateinit var authRepository: AuthRepository
    private lateinit var viewModel: AuthViewModel
    private val testDispatcher = StandardTestDispatcher()

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        authRepository = mockk()
        
        // Mock the token flow
        coEvery { authRepository.getToken() } returns flowOf("test_token")
        
        viewModel = AuthViewModel(authRepository)
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `login success updates state correctly`() = runTest {
        // Given
        val email = "<EMAIL>"
        val password = "password123"
        val mockResponse = mockk<com.yourcompany.driversapp.data.model.LoginResponse> {
            coEvery { token } returns "test_token"
            coEvery { success } returns true
        }
        
        coEvery { authRepository.login(email, password) } returns Result.success(mockResponse)

        // When
        viewModel.login(email, password)
        advanceUntilIdle()

        // Then
        assertTrue(viewModel.loginUiState.value is LoginUiState.Success)
        coVerify { authRepository.login(email, password) }
    }

    @Test
    fun `login failure updates state with error`() = runTest {
        // Given
        val email = "<EMAIL>"
        val password = "wrong_password"
        val errorMessage = "Invalid credentials"
        
        coEvery { authRepository.login(email, password) } returns Result.failure(Exception(errorMessage))

        // When
        viewModel.login(email, password)
        advanceUntilIdle()

        // Then
        val state = viewModel.loginUiState.value
        assertTrue(state is LoginUiState.Error)
        assertEquals(errorMessage, (state as LoginUiState.Error).message)
    }

    @Test
    fun `logout calls repository logout`() = runTest {
        // Given
        coEvery { authRepository.logout() } returns Unit

        // When
        viewModel.logout()
        advanceUntilIdle()

        // Then
        coVerify { authRepository.logout() }
        assertTrue(viewModel.loginUiState.value is LoginUiState.Idle)
    }

    @Test
    fun `clearLoginState resets to idle`() {
        // When
        viewModel.clearLoginState()

        // Then
        assertTrue(viewModel.loginUiState.value is LoginUiState.Idle)
    }
}
