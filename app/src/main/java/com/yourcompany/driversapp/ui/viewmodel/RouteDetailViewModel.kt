package com.yourcompany.driversapp.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.yourcompany.driversapp.data.model.DriverAppViewRoute
import com.yourcompany.driversapp.data.repository.RoutesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

sealed interface RouteDetailUiState {
    object Loading : RouteDetailUiState
    data class Success(val route: DriverAppViewRoute) : RouteDetailUiState
    data class Error(val message: String) : RouteDetailUiState
}

@HiltViewModel
class RouteDetailViewModel @Inject constructor(
    private val routesRepository: RoutesRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow<RouteDetailUiState>(RouteDetailUiState.Loading)
    val uiState: StateFlow<RouteDetailUiState> = _uiState.asStateFlow()

    fun fetchRouteDetail(routeId: String) {
        viewModelScope.launch {
            _uiState.value = RouteDetailUiState.Loading
            
            val result = routesRepository.getRouteDetail(routeId)
            _uiState.value = if (result.isSuccess) {
                RouteDetailUiState.Success(result.getOrNull()!!)
            } else {
                RouteDetailUiState.Error(result.exceptionOrNull()?.message ?: "Failed to fetch route detail")
            }
        }
    }

    fun refresh(routeId: String) {
        fetchRouteDetail(routeId)
    }
}
