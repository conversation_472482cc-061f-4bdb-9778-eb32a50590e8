package com.yourcompany.driversapp.ui.screen.stopdetail

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Camera
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.yourcompany.driversapp.data.model.DriverAppStop
import com.yourcompany.driversapp.ui.components.LoadingButton
import com.yourcompany.driversapp.ui.screen.stopdetail.composables.ImagePickerComposable
import com.yourcompany.driversapp.ui.screen.stopdetail.composables.MapViewComposable
import com.yourcompany.driversapp.ui.screen.stopdetail.composables.SignaturePadComposable
import com.yourcompany.driversapp.ui.viewmodel.StopDetailViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StopDetailScreen(
    routeId: String,
    stopId: String,
    onBackClick: () -> Unit,
    viewModel: StopDetailViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    var grossTonnage by remember { mutableStateOf("") }
    var tareTonnage by remember { mutableStateOf("") }
    var signatureName by remember { mutableStateOf("") }

    // Mock stop data - in real implementation, this would come from navigation arguments or repository
    LaunchedEffect(stopId) {
        val mockStop = DriverAppStop(
            id = stopId,
            address = "123 Main Street, City",
            customerName = "Customer Name",
            productDescription = "Product Description",
            scheduledTime = "10:00 AM",
            tonnesAmount = 25.0,
            isCompleted = false
        )
        viewModel.loadStop(mockStop)
    }

    LaunchedEffect(uiState.error) {
        uiState.error?.let { error ->
            snackbarHostState.showSnackbar(error)
            viewModel.clearError()
        }
    }

    LaunchedEffect(uiState.completionSuccess) {
        if (uiState.completionSuccess) {
            snackbarHostState.showSnackbar("Stop completed successfully!")
            viewModel.clearCompletionSuccess()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Stop Details") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            uiState.stop?.let { stop ->
                // Stop Information Card
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = stop.address,
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold
                        )

                        Text(
                            text = stop.customerName,
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )

                        Text(
                            text = "Product: ${stop.productDescription}",
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Text(
                            text = "Scheduled: ${stop.scheduledTime}",
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Text(
                            text = "Expected: ${stop.tonnesAmount} tonnes",
                            style = MaterialTheme.typography.bodyMedium
                        )

                        if (stop.isCompleted) {
                            Text(
                                text = "✓ COMPLETED",
                                style = MaterialTheme.typography.labelLarge,
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.padding(top = 8.dp)
                            )
                        }
                    }
                }

                // Tonnage Input Card
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Tonnage Information",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )

                        OutlinedTextField(
                            value = grossTonnage,
                            onValueChange = {
                                grossTonnage = it
                                it.toDoubleOrNull()?.let { value ->
                                    viewModel.updateGrossTonnage(value)
                                }
                            },
                            label = { Text("Gross Tonnage") },
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                            modifier = Modifier.fillMaxWidth(),
                            enabled = !stop.isCompleted
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        OutlinedTextField(
                            value = tareTonnage,
                            onValueChange = {
                                tareTonnage = it
                                it.toDoubleOrNull()?.let { value ->
                                    viewModel.updateTareTonnage(value)
                                }
                            },
                            label = { Text("Tare Tonnage") },
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                            modifier = Modifier.fillMaxWidth(),
                            enabled = !stop.isCompleted
                        )

                        stop.netTonnage?.let { net ->
                            Text(
                                text = "Net Tonnage: $net tonnes",
                                style = MaterialTheme.typography.titleSmall,
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.padding(top = 8.dp)
                            )
                        }
                    }
                }

                // Map View
                MapViewComposable(
                    latitude = stop.latitude,
                    longitude = stop.longitude,
                    address = stop.address
                )

                // Photo Capture
                if (!stop.isCompleted) {
                    ImagePickerComposable(
                        onImageSelected = { imageData ->
                            viewModel.setPhoto(imageData)
                            viewModel.uploadPhoto(imageData)
                        }
                    )
                }

                // Signature Pad
                if (!stop.isCompleted) {
                    SignaturePadComposable(
                        onSignatureSaved = { signatureData, name ->
                            viewModel.setSignature(signatureData, name)
                            viewModel.uploadSignature(signatureData, name)
                        }
                    )
                }

                // Complete Stop Button
                if (!stop.isCompleted) {
                    LoadingButton(
                        onClick = { viewModel.markAsComplete() },
                        isLoading = uiState.isCompleting,
                        text = "Complete Stop",
                        loadingText = "Completing...",
                        modifier = Modifier.fillMaxWidth(),
                        enabled = stop.grossTonnage != null && stop.tareTonnage != null
                    )
                }
            }
        }
    }
}
