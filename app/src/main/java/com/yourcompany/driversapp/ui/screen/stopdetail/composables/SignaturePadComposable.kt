package com.yourcompany.driversapp.ui.screen.stopdetail.composables

import android.graphics.Bitmap
import android.graphics.Canvas as AndroidCanvas // Alias to avoid conflict if needed later for bitmap saving
import android.graphics.Paint
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.yourcompany.driversapp.R
import java.io.ByteArrayOutputStream
import androidx.compose.ui.graphics.paths.asAndroidPath

data class PathData(
    val path: Path,
    val color: Color,
    val strokeWidth: Float
)

@Composable
fun SignaturePadComposable(
    onSignatureSaved: (ByteArray, String) -> Unit,
    modifier: Modifier = Modifier
) {
    val paths = remember { mutableStateListOf<PathData>() }
    var currentPath by remember { mutableStateOf(Path()) }
    var showSaveDialog by remember { mutableStateOf(false) }
    var signatureName by remember { mutableStateOf("") }
    
    val strokeColor = MaterialTheme.colorScheme.primary
    val strokeWidth = with(LocalDensity.current) { 3.dp.toPx() }

    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Digital Signature",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // Signature canvas
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp)
                    .background(Color.White, RoundedCornerShape(8.dp))
                    .border(1.dp, MaterialTheme.colorScheme.outline, RoundedCornerShape(8.dp))
                    .clipToBounds()
            ) {
                Canvas(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp)
                        .pointerInput(Unit) {
                            val currentPaths = paths
                            val currentCurrentPath = currentPath
                            val currentStrokeColor = strokeColor
                            val currentStrokeWidth = strokeWidth
                            detectDragGestures(
                                onDragStart = { offset ->
                                    currentCurrentPath = Path().apply {
                                        moveTo(offset.x, offset.y)
                                    }
                                },
                                onDrag = { _, dragAmount ->
                                    currentCurrentPath.relativeLineTo(dragAmount.x, dragAmount.y)
                                },
                                onDragEnd = {
                                    currentPaths.add(
                                        PathData(
                                            path = currentCurrentPath,
                                            color = currentStrokeColor,
                                            strokeWidth = currentStrokeWidth
                                        )
                                    )
                                    currentCurrentPath = Path()
                                }
                            )
                        }
                ) {
                    // Draw all completed paths
                    paths.forEach { pathData ->
                        drawPath(
                            path = pathData.path,
                            color = pathData.color,
                            style = Stroke(
                                width = pathData.strokeWidth,
                                cap = StrokeCap.Round,
                                join = StrokeJoin.Round
                            )
                        )
                    }
                    
                    // Draw current path being drawn
                    drawPath(
                        path = currentPath,
                        color = strokeColor,
                        style = Stroke(
                            width = strokeWidth,
                            cap = StrokeCap.Round,
                            join = StrokeJoin.Round
                        )
                    )
                }

                // Placeholder text when no signature
                if (paths.isEmpty()) {
                    Text(
                        text = "Sign here",
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(
                    onClick = { 
                        paths.clear()
                        currentPath = Path()
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Filled.Clear, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Clear")
                }

                Button(
                    onClick = { 
                        if (paths.isNotEmpty()) {
                            showSaveDialog = true
                        }
                    },
                    modifier = Modifier.weight(1f),
                    enabled = paths.isNotEmpty()
                ) {
                    Icon(Icons.Filled.Save, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Save")
                }
            }
        }
    }

    // Save signature dialog
    if (showSaveDialog) {
        AlertDialog(
            onDismissRequest = { showSaveDialog = false },
            title = { Text("Save Signature") },
            text = {
                Column {
                    Text("Please enter the name for this signature:")
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(
                        value = signatureName,
                        onValueChange = { signatureName = it },
                        label = { Text(stringResource(R.string.signature_name)) },
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        if (signatureName.isNotBlank()) {
                            // Convert signature to bitmap and then to byte array
                            val bitmap = createBitmapFromPaths(paths, 400, 200)
                            val outputStream = ByteArrayOutputStream()
                            bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
                            
                            onSignatureSaved(outputStream.toByteArray(), signatureName)
                            showSaveDialog = false
                            signatureName = ""
                        }
                    },
                    enabled = signatureName.isNotBlank()
                ) {
                    Text("Save")
                }
            },
            dismissButton = {
                TextButton(onClick = { 
                    showSaveDialog = false
                    signatureName = ""
                }) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }
}

private fun createBitmapFromPaths(
    paths: List<PathData>,
    width: Int,
    height: Int
): Bitmap {
    val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
    val canvas = AndroidCanvas(bitmap)
    
    // Fill with white background
    canvas.drawColor(android.graphics.Color.WHITE)
    
    // Draw all paths
    paths.forEach { pathData ->
        val paint = Paint().apply {
            color = pathData.color.toArgb()
            strokeWidth = pathData.strokeWidth
            style = Paint.Style.STROKE
            strokeCap = Paint.Cap.ROUND
            strokeJoin = Paint.Join.ROUND
            isAntiAlias = true
        }
        canvas.drawPath(pathData.path.asAndroidPath(), paint)
    }
    
    return bitmap
}
