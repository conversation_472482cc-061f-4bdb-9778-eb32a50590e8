package com.yourcompany.driversapp.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.yourcompany.driversapp.data.model.DriverAppRouteListItem
import com.yourcompany.driversapp.data.repository.AuthRepository
import com.yourcompany.driversapp.data.repository.RoutesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

sealed interface RoutesListUiState {
    object Loading : RoutesListUiState
    data class Success(val routes: List<DriverAppRouteListItem>) : RoutesListUiState
    data class Error(val message: String) : RoutesListUiState
}

@HiltViewModel
class RoutesListViewModel @Inject constructor(
    private val routesRepository: RoutesRepository,
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow<RoutesListUiState>(RoutesListUiState.Loading)
    val uiState: StateFlow<RoutesListUiState> = _uiState.asStateFlow()

    init {
        fetchRoutes()
    }

    fun fetchRoutes() {
        viewModelScope.launch {
            _uiState.value = RoutesListUiState.Loading
            
            val result = routesRepository.getRoutes()
            _uiState.value = if (result.isSuccess) {
                RoutesListUiState.Success(result.getOrNull() ?: emptyList())
            } else {
                RoutesListUiState.Error(result.exceptionOrNull()?.message ?: "Failed to fetch routes")
            }
        }
    }

    fun logout() {
        viewModelScope.launch {
            authRepository.logout()
        }
    }

    fun refresh() {
        fetchRoutes()
    }
}
