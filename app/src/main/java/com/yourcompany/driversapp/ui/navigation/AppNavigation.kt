package com.yourcompany.driversapp.ui.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.yourcompany.driversapp.ui.screen.login.LoginScreen
import com.yourcompany.driversapp.ui.screen.routedetail.RouteDetailScreen
import com.yourcompany.driversapp.ui.screen.routeslist.RoutesListScreen
import com.yourcompany.driversapp.ui.screen.stopdetail.StopDetailScreen
import com.yourcompany.driversapp.ui.viewmodel.AuthViewModel

@Composable
fun AppNavigation(
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController()
) {
    val authViewModel: AuthViewModel = hiltViewModel()
    val isLoggedIn by authViewModel.isLoggedIn.collectAsState(initial = false)

    NavHost(
        navController = navController,
        startDestination = if (isLoggedIn) Screen.RoutesList.route else Screen.Login.route,
        modifier = modifier
    ) {
        composable(Screen.Login.route) {
            LoginScreen(
                onLoginSuccess = {
                    navController.navigate(Screen.RoutesList.route) {
                        popUpTo(Screen.Login.route) { inclusive = true }
                    }
                }
            )
        }

        composable(Screen.RoutesList.route) {
            RoutesListScreen(
                onRouteClick = { routeId ->
                    navController.navigate(Screen.RouteDetail.createRoute(routeId))
                },
                onLogout = {
                    navController.navigate(Screen.Login.route) {
                        popUpTo(Screen.RoutesList.route) { inclusive = true }
                    }
                }
            )
        }

        composable(Screen.RouteDetail.route) { backStackEntry ->
            val routeId = backStackEntry.arguments?.getString("routeId") ?: ""
            RouteDetailScreen(
                routeId = routeId,
                onStopClick = { stopId ->
                    navController.navigate(Screen.StopDetail.createRoute(routeId, stopId))
                },
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }

        composable(Screen.StopDetail.route) { backStackEntry ->
            val routeId = backStackEntry.arguments?.getString("routeId") ?: ""
            val stopId = backStackEntry.arguments?.getString("stopId") ?: ""
            StopDetailScreen(
                routeId = routeId,
                stopId = stopId,
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
    }
}
