package com.yourcompany.driversapp.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.yourcompany.driversapp.data.model.DriverAppStop
import com.yourcompany.driversapp.data.model.StopCompletionRequest
import com.yourcompany.driversapp.data.repository.RoutesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class StopDetailUiState(
    val stop: DriverAppStop? = null,
    val isLoading: Boolean = false,
    val error: String? = null,
    val isCompleting: Boolean = false,
    val isUploadingPhoto: Boolean = false,
    val isUploadingSignature: Boolean = false,
    val completionSuccess: Boolean = false
)

@HiltViewModel
class StopDetailViewModel @Inject constructor(
    private val routesRepository: RoutesRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(StopDetailUiState())
    val uiState: StateFlow<StopDetailUiState> = _uiState.asStateFlow()

    fun loadStop(stop: DriverAppStop) {
        _uiState.value = _uiState.value.copy(stop = stop, error = null)
    }

    fun updateGrossTonnage(value: Double) {
        val currentStop = _uiState.value.stop ?: return
        val updatedStop = currentStop.copy(grossTonnage = value)
        _uiState.value = _uiState.value.copy(stop = updatedStop)
    }

    fun updateTareTonnage(value: Double) {
        val currentStop = _uiState.value.stop ?: return
        val updatedStop = currentStop.copy(tareTonnage = value)
        _uiState.value = _uiState.value.copy(stop = updatedStop)
    }

    fun setPhoto(imageData: ByteArray) {
        // Convert to base64 or handle as needed
        val currentStop = _uiState.value.stop ?: return
        // For now, just mark that we have a photo
        val updatedStop = currentStop.copy(photoUrl = "local_photo")
        _uiState.value = _uiState.value.copy(stop = updatedStop)
    }

    fun setSignature(imageData: ByteArray, name: String) {
        val currentStop = _uiState.value.stop ?: return
        val updatedStop = currentStop.copy(
            signatureImageBase64 = "signature_data", // Convert to base64
            signatureName = name
        )
        _uiState.value = _uiState.value.copy(stop = updatedStop)
    }

    fun markAsComplete() {
        val currentStop = _uiState.value.stop ?: return
        val grossTonnage = currentStop.grossTonnage ?: return
        val tareTonnage = currentStop.tareTonnage ?: return

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isCompleting = true, error = null)

            val request = StopCompletionRequest(
                stopId = currentStop.id,
                grossTonnage = grossTonnage,
                tareTonnage = tareTonnage,
                isCompleted = true
            )

            val result = routesRepository.completeStop(currentStop.id, request)
            
            if (result.isSuccess) {
                val updatedStop = currentStop.copy(isCompleted = true)
                _uiState.value = _uiState.value.copy(
                    stop = updatedStop,
                    isCompleting = false,
                    completionSuccess = true
                )
            } else {
                _uiState.value = _uiState.value.copy(
                    isCompleting = false,
                    error = result.exceptionOrNull()?.message ?: "Failed to complete stop"
                )
            }
        }
    }

    fun uploadPhoto(imageData: ByteArray) {
        val currentStop = _uiState.value.stop ?: return

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isUploadingPhoto = true, error = null)

            val result = routesRepository.uploadPhoto(currentStop.id, imageData)
            
            _uiState.value = if (result.isSuccess) {
                _uiState.value.copy(isUploadingPhoto = false)
            } else {
                _uiState.value.copy(
                    isUploadingPhoto = false,
                    error = result.exceptionOrNull()?.message ?: "Failed to upload photo"
                )
            }
        }
    }

    fun uploadSignature(imageData: ByteArray, name: String) {
        val currentStop = _uiState.value.stop ?: return

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isUploadingSignature = true, error = null)

            val result = routesRepository.uploadSignature(currentStop.id, imageData, name)
            
            _uiState.value = if (result.isSuccess) {
                _uiState.value.copy(isUploadingSignature = false)
            } else {
                _uiState.value.copy(
                    isUploadingSignature = false,
                    error = result.exceptionOrNull()?.message ?: "Failed to upload signature"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun clearCompletionSuccess() {
        _uiState.value = _uiState.value.copy(completionSuccess = false)
    }
}
