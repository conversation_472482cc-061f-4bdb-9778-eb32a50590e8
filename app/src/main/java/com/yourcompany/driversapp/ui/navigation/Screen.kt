package com.yourcompany.driversapp.ui.navigation

sealed class Screen(val route: String) {
    object Login : Screen("login")
    object RoutesList : Screen("routes_list")
    object RouteDetail : Screen("route_detail/{routeId}") {
        fun createRoute(routeId: String) = "route_detail/$routeId"
    }
    object StopDetail : Screen("stop_detail/{routeId}/{stopId}") {
        fun createRoute(routeId: String, stopId: String) = "stop_detail/$routeId/$stopId"
    }
}
