package com.yourcompany.driversapp.data.repository

import com.yourcompany.driversapp.data.model.DriverAppRouteListItem
import com.yourcompany.driversapp.data.model.DriverAppViewRoute
import com.yourcompany.driversapp.data.model.HaulierRouteDetail
import com.yourcompany.driversapp.data.model.StopCompletionRequest
import com.yourcompany.driversapp.data.model.toDriverAppViewRoute
import com.yourcompany.driversapp.data.remote.ApiService
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.toRequestBody
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class RoutesRepository @Inject constructor(
    private val apiService: ApiService
) {

    suspend fun getRoutes(): Result<List<DriverAppRouteListItem>> {
        return try {
            val response = apiService.getRoutes()
            if (response.isSuccessful) {
                val routes = response.body() ?: emptyList()
                Result.success(routes)
            } else {
                Result.failure(Exception("Failed to fetch routes: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun getRouteDetail(routeId: String): Result<DriverAppViewRoute> {
        return try {
            val response = apiService.getRouteDetail(routeId)
            if (response.isSuccessful) {
                val routeDetail = response.body()
                if (routeDetail != null) {
                    val driverAppRoute = routeDetail.toDriverAppViewRoute()
                    Result.success(driverAppRoute)
                } else {
                    Result.failure(Exception("Route detail not found"))
                }
            } else {
                Result.failure(Exception("Failed to fetch route detail: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun completeStop(stopId: String, request: StopCompletionRequest): Result<Unit> {
        return try {
            val response = apiService.completeStop(stopId, request)
            if (response.isSuccessful) {
                Result.success(Unit)
            } else {
                Result.failure(Exception("Failed to complete stop: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun completeTipStop(stopId: String, request: StopCompletionRequest): Result<Unit> {
        return try {
            val response = apiService.completeTipStop(stopId, request)
            if (response.isSuccessful) {
                Result.success(Unit)
            } else {
                Result.failure(Exception("Failed to complete tip stop: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun uploadPhoto(stopId: String, imageData: ByteArray, fileName: String = "photo.jpg"): Result<Unit> {
        return try {
            val requestBody = imageData.toRequestBody("image/jpeg".toMediaTypeOrNull())
            val photoPart = MultipartBody.Part.createFormData("photo", fileName, requestBody)
            
            val response = apiService.uploadPhoto(stopId, photoPart)
            if (response.isSuccessful) {
                Result.success(Unit)
            } else {
                Result.failure(Exception("Failed to upload photo: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun uploadSignature(
        stopId: String, 
        signatureData: ByteArray, 
        signatureName: String,
        fileName: String = "signature.png"
    ): Result<Unit> {
        return try {
            val signatureRequestBody = signatureData.toRequestBody("image/png".toMediaTypeOrNull())
            val signaturePart = MultipartBody.Part.createFormData("signature", fileName, signatureRequestBody)
            
            val nameRequestBody = signatureName.toRequestBody("text/plain".toMediaTypeOrNull())
            val namePart = MultipartBody.Part.createFormData("name", null, nameRequestBody)
            
            val response = apiService.uploadSignature(stopId, signaturePart, namePart)
            if (response.isSuccessful) {
                Result.success(Unit)
            } else {
                Result.failure(Exception("Failed to upload signature: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
