package com.yourcompany.driversapp.data.model

import kotlinx.serialization.Serializable

@Serializable
data class DriverAppRouteListItem(
    val id: String,
    val routeName: String,
    val customerName: String,
    val scheduledDate: String,
    val status: String,
    val totalStops: Int,
    val completedStops: Int
)

@Serializable
data class HaulierRouteDetail(
    val id: String,
    val routeName: String,
    val customerName: String,
    val scheduledDate: String,
    val status: String,
    val driverName: String? = null,
    val vehicleRegistration: String? = null,
    val routeLines: List<RouteLine> = emptyList()
)

@Serializable
data class RouteLine(
    val id: String,
    val stopId: String,
    val address: String,
    val customerName: String,
    val productDescription: String,
    val scheduledTime: String,
    val tonnesAmount: Double,
    val isCompleted: Boolean = false,
    val photos: List<RouteLinePhoto> = emptyList()
)

@Serializable
data class RouteLinePhoto(
    val id: String,
    val url: String,
    val caption: String? = null,
    val uploadedAt: String
)

@Serializable
data class DriverAppViewRoute(
    val id: String,
    val routeName: String,
    val customerName: String,
    val scheduledDate: String,
    val status: String,
    val stops: List<DriverAppStop> = emptyList()
)

@Serializable
data class DriverAppStop(
    val id: String,
    val address: String,
    val customerName: String,
    val productDescription: String,
    val scheduledTime: String,
    val tonnesAmount: Double,
    var grossTonnage: Double? = null,
    var tareTonnage: Double? = null,
    var isCompleted: Boolean = false,
    var photoUrl: String? = null,
    var signatureImageBase64: String? = null,
    var signatureName: String? = null,
    val latitude: Double? = null,
    val longitude: Double? = null
) {
    val netTonnage: Double?
        get() = if (grossTonnage != null && tareTonnage != null) {
            grossTonnage!! - tareTonnage!!
        } else null
}

@Serializable
data class Stop(
    val id: String,
    val address: String,
    val customerName: String,
    val productDescription: String,
    val scheduledTime: String,
    val tonnesAmount: Double,
    var grossTonnage: Double? = null,
    var tareTonnage: Double? = null,
    var isCompleted: Boolean = false,
    var photoUrl: String? = null,
    var signatureImageBase64: String? = null,
    var signatureName: String? = null,
    val latitude: Double? = null,
    val longitude: Double? = null
) {
    val netTonnage: Double?
        get() = if (grossTonnage != null && tareTonnage != null) {
            grossTonnage!! - tareTonnage!!
        } else null
}

@Serializable
data class Route(
    val id: String,
    val routeName: String,
    val customerName: String,
    val scheduledDate: String,
    val status: String,
    val stops: List<Stop> = emptyList()
)

@Serializable
data class StopCompletionRequest(
    val stopId: String,
    val grossTonnage: Double,
    val tareTonnage: Double,
    val isCompleted: Boolean = true,
    val completedAt: String? = null,
    val notes: String? = null
)

// Extension functions for data conversion
fun HaulierRouteDetail.toDriverAppViewRoute(): DriverAppViewRoute {
    return DriverAppViewRoute(
        id = this.id,
        routeName = this.routeName,
        customerName = this.customerName,
        scheduledDate = this.scheduledDate,
        status = this.status,
        stops = this.routeLines.map { it.toDriverAppStop() }
    )
}

fun RouteLine.toDriverAppStop(): DriverAppStop {
    return DriverAppStop(
        id = this.stopId,
        address = this.address,
        customerName = this.customerName,
        productDescription = this.productDescription,
        scheduledTime = this.scheduledTime,
        tonnesAmount = this.tonnesAmount,
        isCompleted = this.isCompleted
    )
}
