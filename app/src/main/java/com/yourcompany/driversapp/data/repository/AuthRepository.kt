package com.yourcompany.driversapp.data.repository

import com.yourcompany.driversapp.data.local.UserPreferencesManager
import com.yourcompany.driversapp.data.model.LoginRequest
import com.yourcompany.driversapp.data.model.LoginResponse
import com.yourcompany.driversapp.data.remote.ApiService
import kotlinx.coroutines.flow.Flow
import retrofit2.Response
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthRepository @Inject constructor(
    private val apiService: ApiService,
    private val userPreferencesManager: UserPreferencesManager
) {

    suspend fun login(email: String, password: String): Result<LoginResponse> {
        return try {
            val request = LoginRequest(email = email, password = password)
            val response = apiService.login(request)
            
            if (response.isSuccessful) {
                val loginResponse = response.body()
                if (loginResponse != null && loginResponse.success) {
                    // Save token and user info
                    userPreferencesManager.saveToken(loginResponse.token)
                    loginResponse.userId?.let { userPreferencesManager.saveUserId(it) }
                    loginResponse.userName?.let { userPreferencesManager.saveUserName(it) }
                    
                    Result.success(loginResponse)
                } else {
                    Result.failure(Exception(loginResponse?.message ?: "Login failed"))
                }
            } else {
                Result.failure(Exception("Login failed: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun logout() {
        userPreferencesManager.clearAllData()
    }

    fun getToken(): Flow<String?> {
        return userPreferencesManager.getToken()
    }

    fun getUserId(): Flow<String?> {
        return userPreferencesManager.getUserId()
    }

    fun getUserName(): Flow<String?> {
        return userPreferencesManager.getUserName()
    }

    suspend fun isLoggedIn(): Boolean {
        return try {
            val token = userPreferencesManager.getToken()
            // You might want to add token validation logic here
            !token.toString().isNullOrEmpty()
        } catch (e: Exception) {
            false
        }
    }
}
