package com.yourcompany.driversapp.data.remote

import com.yourcompany.driversapp.data.model.DriverAppRouteListItem
import com.yourcompany.driversapp.data.model.HaulierRouteDetail
import com.yourcompany.driversapp.data.model.LoginRequest
import com.yourcompany.driversapp.data.model.LoginResponse
import com.yourcompany.driversapp.data.model.StopCompletionRequest
import okhttp3.MultipartBody
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.Query

interface ApiService {
    
    @POST("Auth/LoginApp")
    suspend fun login(@Body request: LoginRequest): Response<LoginResponse>
    
    @POST("DriverApp/ListRoutesApp")
    suspend fun getRoutes(): Response<List<DriverAppRouteListItem>>
    
    @POST("HaulierRoute/GetRoute")
    suspend fun getRouteDetail(@Query("routeId") routeId: String): Response<HaulierRouteDetail>
    
    @POST("HaulierStop/CompleteStopV3")
    suspend fun completeStop(
        @Query("haulierStopId") stopId: String,
        @Body request: StopCompletionRequest
    ): Response<ResponseBody>
    
    @POST("HaulierStop/CompleteTipStopV3")
    suspend fun completeTipStop(
        @Query("tipStopId") stopId: String,
        @Body request: StopCompletionRequest
    ): Response<ResponseBody>
    
    @Multipart
    @POST("HaulierStop/UploadPhotoV2")
    suspend fun uploadPhoto(
        @Query("haulierStopId") stopId: String,
        @Part photo: MultipartBody.Part
    ): Response<ResponseBody>
    
    @Multipart
    @POST("HaulierStop/UploadSignatureV2")
    suspend fun uploadSignature(
        @Query("haulierStopId") stopId: String,
        @Part signature: MultipartBody.Part,
        @Part name: MultipartBody.Part
    ): Response<ResponseBody>
}
