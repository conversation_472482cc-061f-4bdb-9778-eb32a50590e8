<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme using Material Components (compatible) -->
    <style name="Base.Theme.DriversApp" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>

    <!-- Application theme -->
    <style name="Theme.DriversApp" parent="Base.Theme.DriversApp" />

    <!-- Legacy theme name for compatibility -->
    <style name="Theme.DriversAndroid" parent="Theme.DriversApp" />
</resources>