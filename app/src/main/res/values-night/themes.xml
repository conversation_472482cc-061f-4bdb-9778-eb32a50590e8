<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme for dark mode -->
    <style name="Base.Theme.DriversApp" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorPrimaryVariant">@color/purple_200</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>
</resources>
