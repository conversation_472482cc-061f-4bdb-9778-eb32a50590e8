#!/bin/bash

# Build verification script for Drivers Android App
echo "🔍 Verifying Android project build configuration..."
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "build.gradle.kts" ]; then
    echo "❌ Error: Not in project root directory"
    echo "Please run this script from /Users/<USER>/Code/Drivers-Android"
    exit 1
fi

# Check Java installation
echo "☕ Checking Java installation..."
if command -v java &> /dev/null; then
    echo "✅ Java found:"
    java -version
    echo ""
else
    echo "❌ Java not found. Please run ./setup-java.sh first"
    exit 1
fi

# Check JAVA_HOME
if [ -n "$JAVA_HOME" ]; then
    echo "✅ JAVA_HOME is set: $JAVA_HOME"
else
    echo "⚠️  JAVA_HOME not set, but Java is available"
fi

echo ""
echo "🔧 Checking Gradle wrapper..."
if [ -f "gradlew" ]; then
    echo "✅ Gradle wrapper found"
    chmod +x gradlew
else
    echo "❌ Gradle wrapper not found"
    exit 1
fi

echo ""
echo "📦 Attempting to sync dependencies..."
./gradlew --version

if [ $? -eq 0 ]; then
    echo "✅ Gradle version check successful"
else
    echo "❌ Gradle version check failed"
    exit 1
fi

echo ""
echo "🏗️  Attempting project build..."
./gradlew assembleDebug --stacktrace

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 BUILD SUCCESSFUL!"
    echo "==================="
    echo "✅ Project builds without errors"
    echo "✅ All dependencies resolved"
    echo "✅ KSP plugin working correctly"
    echo "✅ Material 3 theme configured properly"
    echo ""
    echo "📱 Next steps:"
    echo "1. Open Android Studio"
    echo "2. Open this project"
    echo "3. Run the app on a device or emulator"
else
    echo ""
    echo "❌ BUILD FAILED"
    echo "==============="
    echo "Please check the error messages above."
    echo "Common solutions:"
    echo "1. Ensure Java is properly installed"
    echo "2. Check that all dependencies are available"
    echo "3. Try: ./gradlew clean build"
fi
